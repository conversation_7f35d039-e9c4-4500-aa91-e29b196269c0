<?php
// Test the Carcass fetch endpoint
$url = 'http://localhost/lavc/fetch/Carcasses?draw=1&start=0&length=10';

echo "Testing URL: $url\n";

$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 10
    ]
]);

$response = @file_get_contents($url, false, $context);

if ($response === false) {
    echo "Failed to fetch response\n";
    echo "Error: " . error_get_last()['message'] . "\n";
} else {
    echo "Response received:\n";
    echo "Length: " . strlen($response) . "\n";
    echo "Content:\n";
    echo $response . "\n";
    
    // Check if it's valid JSON
    $decoded = json_decode($response, true);
    if ($decoded === null) {
        echo "JSON Error: " . json_last_error_msg() . "\n";
        echo "Raw response (first 500 chars):\n";
        echo substr($response, 0, 500) . "\n";
    } else {
        echo "Valid JSON response\n";
        echo "Keys: " . implode(', ', array_keys($decoded)) . "\n";
    }
}
?>
