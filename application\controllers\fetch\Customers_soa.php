<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Customers_soa extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Customers
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname,
            'port' => $this->db->port
        ];
        $table = 'customer';
        $primary_key = 'id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_name(),
            $this->_get_total_balance(),
            $this->_get_actions()
        ];

        $where = <<<EOT
is_deleted = 0
EOT;


        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $table, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'customer';
            }
        ];
    }

    /**
     * Get customer name
     */
    protected function _get_name()
    {
        return [
            'db' => 'name',
            'dt' => 0,
            'field' => 'name'
        ];
    }


     /**
     * Get Total Balance
     */
    protected function _get_total_balance()
    {
        $CI = &get_instance();
        return [
            'db' => 'id',
            'as' => 'file',
            'dt' => 1,
            'field' => 'file',
            'formatter' => function($d, $row) use($CI){

                $CI->load->model('invoice');
                $invoices = $CI->invoice->get_by_customer($d);
                $total_unpaid = 0;
                $total_invoice = 0;
                $total_paid = 0;

                foreach($invoices as $invoice){
                    $total_paid += $invoice->paid_amount;
                    $total_invoice += $invoice->total;

                }
                $total_unpaid = $total_invoice - $total_paid;
                $res = number_format($total_unpaid,2);

                return $res;

            }
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'id',
            'as' => 'actions',
            'dt' => 2,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div">';
                $res .= anchor('soas/preview/' . $d, 'View Details', 'title="View Details" class="btn btn-primary"');
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
