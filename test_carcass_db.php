<?php
// Test database connection and carcass data
try {
    $pdo = new PDO('mysql:host=localhost;port=3307;dbname=wadcompa_lavc', 'root', '');
    echo "Database connection successful!\n";
    
    // Check if carcass table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'carcass'");
    if ($stmt->rowCount() > 0) {
        echo "Carcass table exists\n";
        
        // Check total records
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM carcass");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Total carcass records: " . $count['count'] . "\n";
        
        // Check non-deleted records
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM carcass WHERE is_deleted = 0");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Active carcass records: " . $count['count'] . "\n";
        
        // Show table structure
        echo "\nTable structure:\n";
        $stmt = $pdo->query("DESCRIBE carcass");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "- " . $row['Field'] . " (" . $row['Type'] . ")\n";
        }
        
        // Show sample records
        echo "\nSample records:\n";
        $stmt = $pdo->query("SELECT * FROM carcass WHERE is_deleted = 0 LIMIT 3");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "ID: " . $row['id'] . ", Customer: " . ($row['customer_name'] ?? 'N/A') . ", SKU: " . ($row['sku'] ?? 'N/A') . "\n";
        }
        
    } else {
        echo "Carcass table does not exist\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
