<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Giblet Model - Simplified for SKU, Size, Range, NO, PCS, KGS
 */
class Giblet extends MYT_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->table_name = 'giblets';
    }


    /**
     * Get all giblets entries
     */
    public function get_all_giblets($where = [], $limit = null, $order_by = null)
    {
        $this->db->select('*')
                 ->from($this->table_name)
                 ->where('is_deleted', 0);

        if (!empty($where)) {
            $this->db->where($where);
        }

        if ($order_by) {
            $this->db->order_by($order_by);
        } else {
            $this->db->order_by('added_on DESC');
        }

        if ($limit) {
            $this->db->limit($limit);
        }

        return $this->db->get()->result();
    }

    /**
     * Get giblets by ID
     */
    public function get_giblets_by_id($id)
    {
        return $this->db->where('id', $id)
                        ->where('is_deleted', 0)
                        ->get($this->table_name)
                        ->row();
    }

    /**
     * Check if SKU already exists
     */
    public function exists($sku, $exclude_id = null)
    {
        $this->db->where('sku', $sku);
        $this->db->where('is_deleted', 0);

        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }

        return $this->db->count_all_results($this->table_name) > 0;
    }

    /**
     * Insert new giblets entry
     */
    public function insert_giblets($data)
    {
        $data['added_on'] = date('Y-m-d H:i:s');
        $data['added_by'] = $_SESSION['user']->id ?? 1;
        $data['is_deleted'] = 0;

        return $this->db->insert($this->table_name, $data);
    }

    /**
     * Update giblets entry
     */
    public function update_giblets($id, $data)
    {
        return $this->db->where('id', $id)
                        ->update($this->table_name, $data);
    }

    /**
     * Soft delete giblets entry (following SKU pattern)
     */
    public function soft_delete_giblets($id, $deleted_by = null)
    {
        $user_id = 1;
        if (isset($_SESSION['user']) && isset($_SESSION['user']->id)) {
            $user_id = $_SESSION['user']->id;
        }

        return $this->db->where([
            'id' => $id,
            'is_deleted' => 0
        ])->update($this->table_name, [
            'is_deleted' => 1
        ]);
    }

    /**
     * Get giblets statistics
     */
    public function get_statistics()
    {
        $stats = [];

        // Total giblets entries
        $stats['total_entries'] = $this->db->where('is_deleted', 0)->count_all_results($this->table_name);

        // Recent entries (last 7 days)
        $stats['recent_entries'] = $this->db->where('is_deleted', 0)
                                           ->where('added_on >=', date('Y-m-d H:i:s', strtotime('-7 days')))
                                           ->count_all_results($this->table_name);

        return $stats;
    }

    /**
     * Search giblets entries
     */
    public function search_giblets($search_term)
    {
        $this->db->select('*')
                 ->from($this->table_name)
                 ->where('is_deleted', 0)
                 ->group_start()
                 ->like('sku', $search_term)
                 ->or_like('size', $search_term)
                 ->or_like('range', $search_term)
                 ->or_like('no', $search_term)
                 ->group_end()
                 ->order_by('added_on DESC');

        return $this->db->get()->result();
    }

    /**
     * Get giblets entries for export
     */
    public function get_giblets_for_export($where = [])
    {
        $this->db->select('sku, size, range, no, pcs, kgs, added_on')
                 ->from($this->table_name)
                 ->where('is_deleted', 0);

        if (!empty($where)) {
            $this->db->where($where);
        }

        $this->db->order_by('added_on DESC');
        return $this->db->get()->result();
    }
} 