<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Trading Billing
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Carcasses extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('carcass');
        $this->load->model('customer');
        $this->load->model('sku');
        $this->load->library('form_validation');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Carcasses Manager
     */
    public function index()
    {
        $this->title = 'Carcass';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/Carcass/manager'
        ];

        $this->build_content('default', 'Carcasses/manager');
    }

    /**
     * Add carcass records (multi-row)
     */
    public function add()
    {
        $rules = $this->config->item('Carcasses/add');

        if ($this->_validate_form($rules) && $this->_attempt_add()) {
            redirect(site_url('Carcasses'));
        } else {
            $this->title = 'Add Carcass';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2.full',
                'js/Carcass/add'
            ];

            $form_error = $this->_error_msg();
            if ($form_error === validation_errors()) {
                $form_error = '';
            }



            // Get available carcass SKUs
            $available_skus = $this->_get_available_skus();

            // Get customer farms
            $customer_farms = $this->_get_customer_farms();

            $data = [
                'form_error' => $form_error,
                'available_skus' => $available_skus,
                'customer_farms' => $customer_farms
            ];
            $this->build_content('default', 'Carcasses/add', $data);
        }
    }

    /**
     * Attempt Add (like hauling_logs)
     */
    protected function _attempt_add()
    {
        $this->db->trans_begin();

        // Get form data arrays
        $skus = $this->input->post('sku') ?: [];
        $sizes = $this->input->post('size') ?: [];
        $ranges = $this->input->post('range') ?: [];
        $nos = $this->input->post('no') ?: [];
        $pcs = $this->input->post('pcs') ?: [];
        $kgs = $this->input->post('kgs') ?: [];

        // Ensure all inputs are arrays for consistent processing
        $skus = is_array($skus) ? $skus : [$skus];
        $sizes = is_array($sizes) ? $sizes : [$sizes];
        $ranges = is_array($ranges) ? $ranges : [$ranges];
        $nos = is_array($nos) ? $nos : [$nos];
        $pcs = is_array($pcs) ? $pcs : [$pcs];
        $kgs = is_array($kgs) ? $kgs : [$kgs];

        // Validate carcass data
        if (!$this->_validate_carcass_data($skus, $sizes, $ranges, $nos, $pcs, $kgs)) {
            $this->db->trans_rollback();
            return false;
        }

        $success_count = 0;
        $max_count = max(count($skus), count($sizes), count($ranges), count($nos), count($pcs), count($kgs));

        for ($i = 0; $i < $max_count; $i++) {
            $sku = isset($skus[$i]) ? trim($skus[$i]) : '';
            $size = isset($sizes[$i]) ? trim($sizes[$i]) : '';
            $range = isset($ranges[$i]) ? trim($ranges[$i]) : '';
            $no = isset($nos[$i]) ? trim($nos[$i]) : '';
            $pcs_value = isset($pcs[$i]) ? trim($pcs[$i]) : '';
            $kgs_value = isset($kgs[$i]) ? trim($kgs[$i]) : '';

            // Skip empty rows
            if (empty($sku) && empty($size) && empty($range) && empty($no) && empty($pcs_value) && empty($kgs_value)) {
                continue;
            }

            // Get trip_no and provide default if empty
            $trip_no = $this->input->post('trip_no');
            if (empty($trip_no)) {
                // Generate numeric trip number (YYYYMMDD + sequence)
                $date_part = date('Ymd'); // e.g., 20250726
                $sequence = str_pad($success_count + 1, 3, '0', STR_PAD_LEFT); // e.g., 001
                $trip_no = (int)($date_part . $sequence); // e.g., 20250726001
            }

            $data = [
                'customer_name' => $this->input->post('customer_farm'),
                'trip_no' => $trip_no,
                'sku' => $sku,
                'size' => $size,
                'range' => $range,
                'no' => !empty($no) ? (int)$no : null,
                'pcs' => !empty($pcs_value) ? (int)$pcs_value : null,
                'kgs' => !empty($kgs_value) ? (float)$kgs_value : null
            ];

            $this->carcass->insert_carcass($data);
            $success_count++;
        }

        $this->db->trans_commit();
        return true;
    }

    /**
     * Get available SKUs that haven't been used yet
     */
    private function _get_available_skus()
    {
        // Get used SKUs from carcass table
        $used_skus = [];
        $used_query = $this->db->select('sku')
                              ->from('carcass')
                              ->where('is_deleted', 0)
                              ->group_by('sku')
                              ->get();

        if ($used_query->num_rows() > 0) {
            foreach ($used_query->result() as $row) {
                $used_skus[] = $row->sku;
            }
        }

        // Get available SKUs excluding used ones
        $this->db->select('sku.sku, sku.size, sku.size_range, customer.name as customer_name');
        $this->db->from('sku');
        $this->db->join('customer', 'customer.id = sku.customer_id', 'left');
        $this->db->where('sku.category', 'Carcass');
        $this->db->where('sku.is_deleted', 0);

        if (!empty($used_skus)) {
            $this->db->where_not_in('sku.sku', $used_skus);
        }

        $this->db->group_by('sku.sku, sku.size, sku.size_range, customer.name');
        $this->db->order_by('customer.name', 'ASC');
        $this->db->order_by('sku.sku', 'ASC');

        return $this->db->get()->result();
    }
    /**
     * Edit a single carcass record
     */
    public function edit($id)
    {
        $carcass = $this->carcass->get_carcass_by_id($id);
        if (!$carcass) {
            $this->session->set_flashdata('error', 'Carcass not found.');
            redirect('Carcasses');
        }

        $this->title = 'Edit Carcass';
        $this->css = [
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/select2/dist/js/select2.full',
            'js/Carcass/add'
        ];

        $data['carcass'] = $carcass;
        $this->build_content('default', 'Carcasses/edit', $data);
    }

    /**
     * Update a single carcass record
     */
    public function update($id)
    {
        $carcass = $this->carcass->get_carcass_by_id($id);
        if (!$carcass) {
            $this->session->set_flashdata('carcass_error', 'Carcass not found.');
            redirect('Carcasses');
        }

        if ($this->input->post()) {
            // Get the main SKU data
            $sku = $this->input->post('sku');
            $size = $this->input->post('size');
            $range = $this->input->post('range');

            // Get array data from the table
            $nos = $this->input->post('no');
            $pcs_array = $this->input->post('pcs');
            $kgs_array = $this->input->post('kgs');

            // Validate required fields
            if (empty($sku) || empty($size) || empty($range)) {
                $this->session->set_flashdata('carcass_error', 'SKU, Size, and Range are required.');
                redirect('Carcasses/edit/' . $id);
                return;
            }

            // For update, we'll use the first row's data (since it's editing a single record)
            $no = isset($nos[0]) ? trim($nos[0]) : '';
            $pcs_value = isset($pcs_array[0]) ? trim($pcs_array[0]) : '';
            $kgs_value = isset($kgs_array[0]) ? trim($kgs_array[0]) : '';

            // Get trip_no and provide default if empty
            $trip_no = $this->input->post('trip_no');
            if (empty($trip_no)) {
                // Generate numeric trip number (YYYYMMDD + random sequence)
                $date_part = date('Ymd'); // e.g., 20250726
                $sequence = str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT); // e.g., 123
                $trip_no = (int)($date_part . $sequence); // e.g., 20250726123
            }

            $data = [
                'customer_name' => $this->input->post('customer_farm'),
                'trip_no' => $trip_no,
                'sku' => $sku,
                'size' => $size,
                'range' => $range,
                'no' => !empty($no) ? (int)$no : null,
                'pcs' => !empty($pcs_value) ? (int)$pcs_value : null,
                'kgs' => !empty($kgs_value) ? (float)$kgs_value : null
            ];

            $this->carcass->update_carcass($id, $data);
            redirect('Carcasses');
        }

        $this->title = 'Edit Carcass';
        $this->css = [
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/select2/dist/js/select2.full',
            'js/Carcass/add'
        ];

        // Get available carcass SKUs (same as add form)
        $available_skus = $this->_get_available_skus();

        $data = [
            'carcass' => $carcass,
            'available_skus' => $available_skus
        ];
        $this->build_content('default', 'Carcasses/edit', $data);
    }

    /**
     * Delete Carcass
     */
    public function delete($carcass_id)
    {
        $where = [
            'id' => $carcass_id,
            'is_deleted' => 0
        ];
        if (!$carcass = $this->carcass->select(null, $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Carcass is not found.'
            ];
        } else {
            // Set delete session lock
            $this->_set_delete_session_lock($carcass_id);

            if (!$this->_attempt_delete($carcass)) {
                http_response_code(400);
                $response = [
                    'message' => $this->_error_msg()
                ];
            } else {
                $response = [
                    'message' => 'Carcass record successfully removed.'
                ];
            }

            $this->_clear_delete_session_lock();
        }

        echo json_encode($response);
    }

    /**
     * Quick update specific record
     */
    public function quick_update()
    {
        // Find the record with SKU="hey", Size="x", Range="x", NO=1, PCS=1, KGS=1.00
        $record = $this->db->where([
            'sku' => 'hey',
            'size' => 'x',
            'range' => 'x',
            'no' => 1,
            'pcs' => 1,
            'kgs' => 1.00,
            'is_deleted' => 0
        ])->get('carcass')->row();

        if ($record) {
            // Update to NO=3, PCS=3, KGS=3.00 and update timestamp to put it at the top
            $updated = $this->db->where('id', $record->id)->update('carcass', [
                'no' => 3,
                'pcs' => 3,
                'kgs' => 3.00,
                'added_on' => date('Y-m-d H:i:s')  // Update timestamp to make it newest
            ]);

            if ($updated) {
                echo json_encode(['success' => true, 'message' => 'Record updated successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to update record']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Record not found']);
        }
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($carcass)
    {
        // Start transaction
        $this->db->trans_start();

        try {
            // Use model method to delete carcass
            $result = $this->carcass->soft_delete_carcass($carcass->id, $_SESSION['user']->id ?? 1);

            if (!$result) {
                $this->_error = 'Failed to delete carcass record';
                $this->db->trans_rollback();
                return false;
            }

            // Complete transaction
            $this->db->trans_complete();
            if ($this->db->trans_status() === FALSE) {
                $this->_error = 'transaction_failed';
                return false;
            }

            return true;
        } catch (Exception $e) {
            $this->db->trans_rollback();
            $this->_error = 'database_error: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * Check if a delete session is currently active
     */
    protected function _is_delete_session_active()
    {
        $deleting_carcass_id = $this->session->userdata('deleting_carcass_id');
        $delete_timestamp = $this->session->userdata('delete_timestamp');
        if (empty($deleting_carcass_id) || empty($delete_timestamp)) {
            return false;
        }
        $timeout = 5 * 60;
        if (time() - $delete_timestamp > $timeout) {
            $this->_clear_delete_session_lock();
            return false;
        }

        return true;
    }

    /**
     * Set delete session lock
     */
    protected function _set_delete_session_lock($carcass_id)
    {
        $this->session->set_userdata([
            'deleting_carcass_id' => $carcass_id,
            'delete_timestamp' => time()
        ]);
    }

    /**
     * Clear delete session lock
     */
    protected function _clear_delete_session_lock()
    {
        $this->session->unset_userdata(['deleting_carcass_id', 'delete_timestamp']);
    }

    /**
     * Validate Form (like hauling_logs)
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Validate Carcass Data (like hauling_logs validation)
     */
    protected function _validate_carcass_data($skus, $sizes, $ranges, $nos, $pcs, $kgs)
    {
        $max_count = max(count($nos), count($pcs), count($kgs));

        // Get the selected SKU to check for duplicates
        $selected_sku = $this->input->post('sku');

        // Check if this SKU already exists in the carcass table
        if (!empty($selected_sku)) {
            $existing_sku = $this->db->where('sku', $selected_sku)
                                   ->where('is_deleted', 0)
                                   ->get('carcass')
                                   ->row();

            if ($existing_sku) {
                $this->_error = 'SKU "' . $selected_sku . '" already exists in the carcass records. Each SKU must be unique.';
                return false;
            }
        }

        for ($i = 0; $i < $max_count; $i++) {
            $no = isset($nos[$i]) ? trim($nos[$i]) : '';
            $pcs_value = isset($pcs[$i]) ? trim($pcs[$i]) : '';
            $kgs_value = isset($kgs[$i]) ? trim($kgs[$i]) : '';

            // Skip completely empty rows
            if (empty($no) && empty($pcs_value) && empty($kgs_value)) {
                continue;
            }

            // Validate numeric fields (like hauling_logs)
            if (!empty($pcs_value) && !is_numeric($pcs_value)) {
                $this->_error = 'PCS must be a valid number.';
                return false;
            }

            if (!empty($kgs_value) && !is_numeric($kgs_value)) {
                $this->_error = 'KGS must be a valid number.';
                return false;
            }

            // Validate that numeric fields are greater than or equal to 0 (like hauling_logs)
            if (!empty($pcs_value) && (float)$pcs_value < 0) {
                $this->_error = 'PCS must be greater than or equal to 0.';
                return false;
            }

            if (!empty($kgs_value) && (float)$kgs_value < 0) {
                $this->_error = 'KGS must be greater than or equal to 0.';
                return false;
            }
        }

        return true;
    }

    /**
     * Get customer farm names for the dropdown
     */
    protected function _get_customer_farms()
    {
        $this->load->model('customer');
        return $this->db->select('id as customer_id, name as customer_farm_name')
                        ->from('customer')
                        ->where('is_deleted', 0)
                        ->order_by('name', 'ASC')
                        ->get()
                        ->result();
    }

    /**
     * Get trips for a specific customer (AJAX endpoint - like Hauling Log)
     */
    public function get_trips_by_customer($customer_id)
    {
        $this->load->model('schedule');
        $trips = $this->schedule->get_trips_by_customer($customer_id);

        echo json_encode([
            'success' => true,
            'trips' => $trips
        ]);
    }

    /**
     * Check carcass table structure
     */
    public function check_table_structure()
    {
        $query = $this->db->query("DESCRIBE carcass");
        $columns = $query->result();

        echo "<h3>Carcass Table Columns:</h3>";
        echo "<pre>";
        foreach ($columns as $column) {
            echo $column->Field . " - " . $column->Type . "\n";
        }
        echo "</pre>";

        // Also check if there's any data
        $count = $this->db->count_all('carcass');
        echo "<p>Total records: " . $count . "</p>";

        // Show sample data
        $sample = $this->db->limit(3)->get('carcass')->result();
        echo "<h3>Sample Data:</h3>";
        echo "<pre>";
        print_r($sample);
        echo "</pre>";
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            case 'transaction_failed':
                return '<p>Transaction failed. Please try again.</p>';

            case 'server_error':
                return '';

            default:
                if (strpos($this->_error, 'database_error:') === 0) {
                    return '<p>' . htmlspecialchars($this->_error) . '</p>';
                }

                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . htmlspecialchars($error) . '</p>';
        }
    }
}
