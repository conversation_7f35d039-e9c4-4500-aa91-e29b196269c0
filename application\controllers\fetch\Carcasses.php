<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Carcasses extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch carcasses
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname,
            'port' => $this->db->port
        ];
        $table = 'carcass';
        $primary_key = 'carcass.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_customer_farm(),
            $this->_get_trip_no(),
            $this->_get_sku(),
            $this->_get_size(),
            $this->_get_range(),
            $this->_get_no(),
            $this->_get_pcs(),
            $this->_get_kgs(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
carcass
LEFT JOIN customer ON customer.id = carcass.customer_name
LEFT JOIN user AS added_by_user ON added_by_user.id = carcass.added_by
EOT;

        $where = <<<EOT
carcass.is_deleted = 0
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Manual fetch implementation
     */
    protected function _manual_fetch()
    {
        try {
            $draw = intval($this->input->get_post('draw'));
            $start = intval($this->input->get_post('start'));
            $length = intval($this->input->get_post('length'));
            $search_value = $this->input->get_post('search')['value'];

            // Optimized query - join with customer table to get customer name
            $this->db->select('
                carcass.id,
                COALESCE(customer.name, carcass.customer_name) as customer_name,
                carcass.trip_no,
                carcass.sku,
                carcass.size,
                carcass.range,
                carcass.no,
                carcass.pcs,
                carcass.kgs
            ');
            $this->db->from('carcass');
            $this->db->join('customer', 'customer.id = carcass.customer_name', 'left');
            $this->db->where('carcass.is_deleted', 0);

            if (!empty($search_value)) {
                $this->db->group_start();
                $this->db->like('customer.name', $search_value);
                $this->db->or_like('carcass.trip_no', $search_value);
                $this->db->or_like('carcass.sku', $search_value);
                $this->db->or_like('carcass.size', $search_value);
                $this->db->or_like('carcass.range', $search_value);
                $this->db->group_end();
            }

            // Get total count for filtering
            $total_query = clone $this->db;
            $total_records = $total_query->count_all_results();

            if ($length > 0) {
                $this->db->limit($length, $start);
            }

            // Order by NO field ascending - lowest numbers at top
            $this->db->order_by('carcass.no', 'ASC');
            $query = $this->db->get();
            $data = [];

            if ($query->num_rows() > 0) {
                $results = $query->result();
                $data = array(); // Pre-allocate array

                foreach ($results as $row) {
                    // Optimized data processing - minimal function calls
                    $data[] = [
                        'DT_RowId' => 'carcass_' . $row->id,
                        'DT_RowClass' => 'carcass',
                        '0' => $row->customer_name ?: 'N/A',    // Customer/Farm
                        '1' => $row->trip_no ?: 'N/A',          // Trip No.
                        '2' => $row->sku,                       // SKU
                        '3' => $row->size,                      // Size
                        '4' => $row->range,                     // Range
                        '5' => $row->no,                        // NO
                        '6' => $row->pcs,                       // PCS
                        '7' => $row->kgs,                       // KGS
                        '8' => $this->_generate_actions($row->id) // Actions
                    ];
                }

                // Free memory
                unset($results);
            }

            // Get total records count
            $this->db->from('carcass');
            $this->db->where('carcass.is_deleted', 0);
            $records_total = $this->db->count_all_results();

            // Optimized response
            $response = [
                'draw' => $draw,
                'recordsTotal' => $records_total,
                'recordsFiltered' => $total_records,
                'data' => $data
            ];

            // Set proper headers for performance
            header('Content-Type: application/json; charset=utf-8');
            header('Cache-Control: no-cache, must-revalidate');

            echo json_encode($response, JSON_UNESCAPED_UNICODE);

        } catch (Exception $e) {
            $response = [
                'draw' => intval($this->input->get_post('draw')),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => [],
                'error' => $e->getMessage()
            ];
            echo json_encode($response);
        }
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'carcass.id',
            'dt' => 'DT_RowId',
            'formatter' => function ($d, $row) {
                return 'carcass_' . $d;
            }
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'carcass.id',
            'dt' => 'DT_RowClass',
            'formatter' => function ($d, $row) {
                return 'carcass';
            }
        ];
    }

    /**
     * Get Customer Farm
     */
    protected function _get_customer_farm()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer_name',
            'dt' => 'customer_farm'
        ];
    }

    /**
     * Get Trip No
     */
    protected function _get_trip_no()
    {
        return [
            'db' => 'carcass.trip_no',
            'dt' => 'trip_no'
        ];
    }

    /**
     * Get SKU
     */
    protected function _get_sku()
    {
        return [
            'db' => 'carcass.sku',
            'dt' => 'sku'
        ];
    }

    /**
     * Get Size
     */
    protected function _get_size()
    {
        return [
            'db' => 'carcass.size',
            'dt' => 'size'
        ];
    }

    /**
     * Get Range
     */
    protected function _get_range()
    {
        return [
            'db' => 'carcass.range',
            'dt' => 'range'
        ];
    }

    /**
     * Get No
     */
    protected function _get_no()
    {
        return [
            'db' => 'carcass.no',
            'dt' => 'no'
        ];
    }

    /**
     * Get PCS
     */
    protected function _get_pcs()
    {
        return [
            'db' => 'carcass.pcs',
            'dt' => 'pcs'
        ];
    }

    /**
     * Get KGS
     */
    protected function _get_kgs()
    {
        return [
            'db' => 'carcass.kgs',
            'dt' => 'kgs'
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'carcass.id',
            'dt' => 'actions',
            'formatter' => function ($d, $row) {
                return $this->_generate_actions($d);
            }
        ];
    }

    /**
     * Generate actions for a carcass record
     */
    protected function _generate_actions($carcass_id)
    {
        $res = '<div class="dropdown text-right">';
        $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">';
        $res .= 'Actions <span class="caret"></span></button>';
        $res .= '<div class="dropdown-menu dropdown-menu-right">';

        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_SUPERVISOR])) {
            $res .= anchor(
                'Carcasses/edit/' . $carcass_id,
                'Edit',
                'title="Edit" class="dropdown-item text-left"'
            );
            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $carcass_id . '">Delete</button>';
        }
        $res .= '</div></div>';
        return $res;
    }



}
