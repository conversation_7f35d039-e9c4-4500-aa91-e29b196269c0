<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Users extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            die(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Users
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname,
            'port' => $this->db->port
        ];
        $table = 'user';
        $primary_key = 'user.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_full_name(),
            $this->_get_username(),
            $this->_get_role(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
user
LEFT JOIN role ON role.id = user.role_id
EOT;

        $where = <<<EOT
user.is_deleted = 0
    AND (user.username NOT REGEXP 'myt_')
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'user.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'user.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'user';
            }
        ];
    }

    /**
     * Get full name
     */
    protected function _get_full_name()
    {
        return [
            'db' => 'user.full_name',
            'dt' => 0,
            'field' => 'full_name'
        ];
    }

    /**
     * Get username
     */
    protected function _get_username()
    {
        return [
            'db' => 'user.username',
            'dt' => 1,
            'field' => 'username'
        ];
    }

    /**
     * Get role
     */
    protected function _get_role()
    {
        return [
            'db' => 'role.name',
            'as' => 'role',
            'dt' => 2,
            'field' => 'role'
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'user.id',
            'as' => 'actions',
            'dt' => 3,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                    
                if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                    $res .= anchor('users/reset_password/' . $d, 'Reset Password', 'title="Reset Password" class="dropdown-item text-left"');
                    $res .= anchor('users/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                    $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                }

                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
