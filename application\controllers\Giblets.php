<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Trading Billing
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Giblets extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('giblet');
        $this->load->model('customer');
        $this->load->model('sku');
        $this->load->library('form_validation');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Index
     */
    public function index()
    {
        // Clear any lingering validation errors and session data
        $this->form_validation->reset_validation();
        $this->session->unset_userdata('_ci_validation_errors');

        // Check and add missing columns if needed
        $this->_ensure_table_columns();

        $this->title = 'Giblets';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/Giblets/manager'
        ];
        $this->build_content('default', 'Giblets/manager');
    }

    /**
     * Add giblets records (multi-row)
     */
    public function add()
    {
        $rules = $this->config->item('Giblets/add');

        if ($this->_validate_form($rules) && $this->_attempt_add()) {
            redirect(site_url('Giblets'));
        } else {
            $this->title = 'Add Giblets';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2.full',
                'js/Giblets/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            // Get available giblets SKUs
            $available_skus = $this->_get_available_skus();

            // Get customer farms
            $customer_farms = $this->_get_customer_farms();

            $data = [
                'form_error' => $form_error,
                'available_skus' => $available_skus,
                'customer_farms' => $customer_farms
            ];
            $this->build_content('default', 'Giblets/add', $data);
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $this->db->trans_begin();

        $skus = $this->input->post('sku') ?: [];
        $sizes = $this->input->post('size') ?: [];
        $ranges = $this->input->post('range') ?: [];
        $nos = $this->input->post('no') ?: [];
        $pcs = $this->input->post('pcs') ?: [];
        $kgs = $this->input->post('kgs') ?: [];

        if (!is_array($skus)) {
            $skus = [$skus];
        }
        if (!is_array($sizes)) {
            $sizes = [$sizes];
        }
        if (!is_array($ranges)) {
            $ranges = [$ranges];
        }
        if (!is_array($nos)) {
            $nos = [$nos];
        }
        if (!is_array($pcs)) {
            $pcs = [$pcs];
        }
        if (!is_array($kgs)) {
            $kgs = [$kgs];
        }

        // Validate giblets data
        if (!$this->_validate_giblets_data($skus, $sizes, $ranges, $nos, $pcs, $kgs)) {
            $this->db->trans_rollback();
            return false;
        }

        $success_count = 0;
        $max_count = max(count($skus), count($sizes), count($ranges), count($nos), count($pcs), count($kgs));

        for ($i = 0; $i < $max_count; $i++) {
            $sku = isset($skus[$i]) ? trim($skus[$i]) : '';
            $size = isset($sizes[$i]) ? trim($sizes[$i]) : '';
            $range = isset($ranges[$i]) ? trim($ranges[$i]) : '';
            $no = isset($nos[$i]) ? trim($nos[$i]) : '';
            $pcs_value = isset($pcs[$i]) ? trim($pcs[$i]) : '';
            $kgs_value = isset($kgs[$i]) ? trim($kgs[$i]) : '';

            // Skip empty rows
            if (empty($sku) && empty($size) && empty($range) && empty($no) && empty($pcs_value) && empty($kgs_value)) {
                continue;
            }

            // Get trip_no and provide default if empty
            $trip_no = $this->input->post('trip_no');
            if (empty($trip_no)) {
                // Generate numeric trip number (YYYYMMDD + sequence)
                $date_part = date('Ymd'); // e.g., 20250726
                $sequence = str_pad($success_count + 1, 3, '0', STR_PAD_LEFT); // e.g., 001
                $trip_no = (int)($date_part . $sequence); // e.g., 20250726001
            }

            $data = [
                'customer_name' => $this->input->post('customer_farm'),
                'trip_no' => $trip_no,
                'sku' => $sku,
                'size' => $size,
                'range' => $range,
                'no' => !empty($no) ? (int)$no : null,
                'pcs' => !empty($pcs_value) ? (int)$pcs_value : null,
                'kgs' => !empty($kgs_value) ? (float)$kgs_value : null
            ];

            $this->giblet->insert_giblets($data);
            $success_count++;
        }

        $this->db->trans_commit();
        return true;
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Validate Giblets Data (like hauling_logs validation)
     */
    protected function _validate_giblets_data($skus, $sizes, $ranges, $nos, $pcs, $kgs)
    {
        $max_count = max(count($nos), count($pcs), count($kgs));

        for ($i = 0; $i < $max_count; $i++) {
            $no = isset($nos[$i]) ? trim($nos[$i]) : '';
            $pcs_value = isset($pcs[$i]) ? trim($pcs[$i]) : '';
            $kgs_value = isset($kgs[$i]) ? trim($kgs[$i]) : '';

            // Skip completely empty rows
            if (empty($no) && empty($pcs_value) && empty($kgs_value)) {
                continue;
            }

            // Validate numeric fields (like hauling_logs)
            if (!empty($pcs_value) && !is_numeric($pcs_value)) {
                $this->_error = 'PCS must be a valid number.';
                return false;
            }

            if (!empty($kgs_value) && !is_numeric($kgs_value)) {
                $this->_error = 'KGS must be a valid number.';
                return false;
            }

            // Validate that numeric fields are greater than or equal to 0 (like hauling_logs)
            if (!empty($pcs_value) && (float)$pcs_value < 0) {
                $this->_error = 'PCS must be greater than or equal to 0.';
                return false;
            }

            if (!empty($kgs_value) && (float)$kgs_value < 0) {
                $this->_error = 'KGS must be greater than or equal to 0.';
                return false;
            }
        }

        return true;
    }

    /**
     * Edit a single giblets record
     */
    public function edit($id)
    {
        $giblets = $this->giblet->get_giblets_by_id($id);
        if (!$giblets) {
            $this->session->set_flashdata('error', 'Giblets not found.');
            redirect('Giblets');
        }

        $this->title = 'Edit Giblets';
        $this->css = [
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/select2/dist/js/select2.full',
            'js/Giblets/add'
        ];

        $data['giblets'] = $giblets;
        $this->build_content('default', 'Giblets/edit', $data);
    }

    /**
     * Update a single giblets record
     */
    public function update($id)
    {
        $giblets = $this->giblet->get_giblets_by_id($id);
        if (!$giblets) {
            $this->session->set_flashdata('giblets_error', 'Giblets not found.');
            redirect('Giblets');
        }

        $rules = $this->config->item('Giblets/edit');

        if ($this->_validate_form($rules) && $this->_attempt_edit($id)) {
            // Clear any validation errors before redirect
            $this->form_validation->reset_validation();
            redirect(site_url('Giblets'));
        } else {
            $data = [
                'giblets' => $giblets
            ];
            $this->build_content('default', 'Giblets/edit', $data);
        }
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($id)
    {
        // Get the main SKU data
        $sku = $this->input->post('sku');
        $size = $this->input->post('size');
        $range = $this->input->post('range');

        // Get array data from the table
        $nos = $this->input->post('no');
        $pcs_array = $this->input->post('pcs');
        $kgs_array = $this->input->post('kgs');

        // Additional validation for numeric fields
        if (!empty($pcs_array[0]) && !is_numeric($pcs_array[0])) {
            $this->_error = 'PCS must be a valid number.';
            return false;
        }

        if (!empty($kgs_array[0]) && !is_numeric($kgs_array[0])) {
            $this->_error = 'KGS must be a valid number.';
            return false;
        }

        // For update, we'll use the first row's data (since it's editing a single record)
        $no = isset($nos[0]) ? trim($nos[0]) : '';
        $pcs_value = isset($pcs_array[0]) ? trim($pcs_array[0]) : '';
        $kgs_value = isset($kgs_array[0]) ? trim($kgs_array[0]) : '';

        $data = [
            'sku' => $sku,
            'size' => $size,
            'range' => $range,
            'no' => !empty($no) ? (int)$no : null,
            'pcs' => !empty($pcs_value) ? (int)$pcs_value : null,
            'kgs' => !empty($kgs_value) ? (float)$kgs_value : null
        ];

        $this->giblet->update_giblets($id, $data);
        return true;

        $this->title = 'Edit Giblets';
        $data['giblets'] = $giblets;
        $this->build_content('default', 'Giblets/edit', $data);
    }

    /**
     * Delete Giblets
     */
    public function delete($giblets_id)
    {
        $where = [
            'id' => $giblets_id,
            'is_deleted' => 0
        ];
        if (!$giblets = $this->giblet->select(null, $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Giblets is not found.'
            ];
        } else {
            // Set delete session lock
            $this->_set_delete_session_lock($giblets_id);

            if (!$this->_attempt_delete($giblets)) {
                http_response_code(400);
                $response = [
                    'message' => $this->_error_msg()
                ];
            } else {
                $response = [
                    'message' => 'Giblets record successfully removed.'
                ];
            }

            $this->_clear_delete_session_lock();
        }

        echo json_encode($response);
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($giblets)
    {
        $this->db->trans_start();

        $result = $this->giblet->soft_delete_giblets($giblets->id, $_SESSION['user']->id ?? 1);

        if (!$result) {
            $this->_error = 'Failed to delete giblets record';
            $this->db->trans_rollback();
            return false;
        }

        $this->db->trans_complete();
        if ($this->db->trans_status() === FALSE) {
            $this->_error = 'transaction_failed';
            return false;
        }

        return true;
    }

    /**
     * Set delete session lock
     */
    protected function _set_delete_session_lock($giblets_id)
    {
        $this->session->set_userdata([
            'deleting_giblets_id' => $giblets_id,
            'delete_timestamp' => time()
        ]);
    }

    /**
     * Clear delete session lock
     */
    protected function _clear_delete_session_lock()
    {
        $this->session->unset_userdata(['deleting_giblets_id', 'delete_timestamp']);
    }

    /**
     * Get available SKUs for giblets that haven't been used yet
     */
    protected function _get_available_skus()
    {
        // Get used SKUs from giblets table
        $used_skus = [];
        $used_query = $this->db->select('sku')
                              ->from('giblets')
                              ->where('is_deleted', 0)
                              ->group_by('sku')
                              ->get();

        if ($used_query->num_rows() > 0) {
            foreach ($used_query->result() as $row) {
                $used_skus[] = $row->sku;
            }
        }

        // Get available SKUs excluding used ones
        $this->db->select('sku.sku, sku.size, sku.size_range, customer.name as customer_name');
        $this->db->from('sku');
        $this->db->join('customer', 'customer.id = sku.customer_id', 'left');
        $this->db->where('sku.category', 'Giblets');
        $this->db->where('sku.is_deleted', 0);

        if (!empty($used_skus)) {
            $this->db->where_not_in('sku.sku', $used_skus);
        }

        $this->db->group_by('sku.sku, sku.size, sku.size_range, customer.name');
        $this->db->order_by('customer.name', 'ASC');
        $this->db->order_by('sku.sku', 'ASC');

        return $this->db->get()->result();
    }

    /**
     * Get customer farm names for the dropdown
     */
    protected function _get_customer_farms()
    {
        $this->load->model('customer');
        $result = $this->db->select('id as customer_id, name as customer_farm_name')
                        ->from('customer')
                        ->where('is_deleted', 0)
                        ->order_by('name', 'ASC')
                        ->get()
                        ->result();

        // Debug: Log the query and results
        log_message('debug', 'Customer farms query: ' . $this->db->last_query());
        log_message('debug', 'Customer farms result: ' . json_encode($result));

        return $result;
    }

    /**
     * Ensure giblets table has required columns
     */
    protected function _ensure_table_columns()
    {
        try {
            // Check if customer_name column exists
            $fields = $this->db->list_fields('giblets');

            if (!in_array('customer_name', $fields)) {
                // Add customer_name column
                $this->db->query("ALTER TABLE giblets ADD COLUMN customer_name VARCHAR(255) NULL AFTER id");
            }

            if (!in_array('trip_no', $fields)) {
                // Add trip_no column
                $this->db->query("ALTER TABLE giblets ADD COLUMN trip_no BIGINT NULL AFTER customer_name");
            }

        } catch (Exception $e) {
            // Log error but don't break the application
            log_message('error', 'Failed to add columns to giblets table: ' . $e->getMessage());
        }
    }

    /**
     * Check giblets table structure (for debugging)
     */
    public function check_table_structure()
    {
        $query = $this->db->query("DESCRIBE giblets");
        $columns = $query->result();

        echo "<h3>Giblets Table Columns:</h3>";
        echo "<pre>";
        foreach ($columns as $column) {
            echo $column->Field . " - " . $column->Type . "\n";
        }
        echo "</pre>";

        // Also check if there's any data
        $count = $this->db->count_all('giblets');
        echo "<p>Total records: " . $count . "</p>";

        // Show sample data
        $sample = $this->db->limit(3)->get('giblets')->result();
        echo "<h3>Sample Data:</h3>";
        echo "<pre>";
        print_r($sample);
        echo "</pre>";

        // Test the fetch query directly
        echo "<h3>Test Fetch Query:</h3>";
        $this->db->select('
            giblets.id,
            giblets.customer_name,
            giblets.trip_no,
            giblets.sku,
            giblets.size,
            giblets.range,
            giblets.no,
            giblets.pcs,
            giblets.kgs
        ');
        $this->db->from('giblets');
        $this->db->where('giblets.is_deleted', 0);
        $this->db->limit(3);

        $test_query = $this->db->get();
        echo "<p>Query: " . $this->db->last_query() . "</p>";
        echo "<pre>";
        print_r($test_query->result());
        echo "</pre>";
    }

    /**
     * Test AJAX fetch directly
     */
    public function test_fetch()
    {
        header('Content-Type: application/json');

        // Simulate the fetch request
        $this->db->select('
            giblets.id,
            giblets.customer_name,
            giblets.trip_no,
            giblets.sku,
            giblets.size,
            giblets.range,
            giblets.no,
            giblets.pcs,
            giblets.kgs
        ');
        $this->db->from('giblets');
        $this->db->where('giblets.is_deleted', 0);
        $this->db->limit(10);

        $query = $this->db->get();
        $data = array();

        if ($query->num_rows() > 0) {
            $results = $query->result();

            foreach ($results as $row) {
                $data[] = [
                    'DT_RowId' => 'giblets_' . $row->id,
                    'DT_RowClass' => 'giblets',
                    '0' => $row->customer_name ?: 'N/A',
                    '1' => $row->trip_no ?: 'N/A',
                    '2' => $row->sku,
                    '3' => $row->size,
                    '4' => $row->range,
                    '5' => $row->no,
                    '6' => $row->pcs,
                    '7' => $row->kgs,
                    '8' => '<button class="btn btn-sm btn-danger action-delete" data-id="' . $row->id . '">Delete</button>'
                ];
            }
        }

        $response = [
            'draw' => 1,
            'recordsTotal' => $query->num_rows(),
            'recordsFiltered' => $query->num_rows(),
            'data' => $data
        ];

        echo json_encode($response);
    }

    /**
     * Debug customer farms
     */
    public function debug_customers()
    {
        header('Content-Type: application/json');

        $customer_farms = $this->_get_customer_farms();

        echo json_encode([
            'customer_farms' => $customer_farms,
            'count' => count($customer_farms),
            'query' => $this->db->last_query()
        ]);
    }

    /**
     * Get trip numbers for a specific customer (AJAX)
     */
    public function get_trip_numbers()
    {
        header('Content-Type: application/json');

        $customer_id = $this->input->post('customer_id');

        if (!$customer_id) {
            echo json_encode(['success' => false, 'message' => 'Customer ID is required']);
            return;
        }

        // Get trip numbers from hauling_logs for this customer
        $this->db->select('trip_no');
        $this->db->from('hauling_logs');
        $this->db->where('customer_id', $customer_id);
        $this->db->where('is_deleted', 0);
        $this->db->group_by('trip_no');
        $this->db->order_by('trip_no', 'DESC');

        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            $trips = $query->result();
            echo json_encode(['success' => true, 'trips' => $trips]);
        } else {
            echo json_encode(['success' => true, 'trips' => []]);
        }
    }
}