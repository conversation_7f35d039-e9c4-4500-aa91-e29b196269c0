<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Giblets extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch giblets
     */
    public function index()
    {
        ob_clean();
        header('Content-Type: application/json');
        
        try {
            $draw = intval($this->input->get_post('draw'));
            $start = intval($this->input->get_post('start'));
            $length = intval($this->input->get_post('length'));
            $search_value = $this->input->get_post('search')['value'];

            // Select all required columns
            $this->db->select('
                giblets.id,
                giblets.customer_name,
                giblets.trip_no,
                giblets.sku,
                giblets.size,
                giblets.range,
                giblets.no,
                giblets.pcs,
                giblets.kgs
            ');
            $this->db->from('giblets');
            $this->db->where('giblets.is_deleted', 0);

            if (!empty($search_value)) {
                $this->db->group_start();
                $this->db->like('giblets.customer_name', $search_value);
                $this->db->or_like('giblets.trip_no', $search_value);
                $this->db->or_like('giblets.sku', $search_value);
                $this->db->or_like('giblets.size', $search_value);
                $this->db->or_like('giblets.range', $search_value);
                $this->db->group_end();
            }

            $query = $this->db->get();
            $data = array();

            if ($query->num_rows() > 0) {
                $results = $query->result();
                
                foreach ($results as $row) {
                    // Create array with 9 columns to match table headers
                    // Headers: Customer/Farm, Trip No., SKU, Size, Range, NO, PCS, KGS, Actions (9 columns total)
                    $data[] = [
                        'DT_RowId' => 'giblets_' . $row->id,
                        'DT_RowClass' => 'giblets',
                        '0' => $row->customer_name ?: 'N/A',    // Customer/Farm
                        '1' => $row->trip_no ?: 'N/A',          // Trip No.
                        '2' => $row->sku,                       // SKU
                        '3' => $row->size,                      // Size
                        '4' => $row->range,                     // Range
                        '5' => $row->no,                        // NO
                        '6' => $row->pcs,                       // PCS
                        '7' => $row->kgs,                       // KGS
                        '8' => $this->_generate_actions($row->id) // Actions
                    ];
                }
            }

            // Get total records count
            $this->db->from('giblets');
            $this->db->where('giblets.is_deleted', 0);
            $records_total = $this->db->count_all_results();

            $response = [
                'draw' => $draw,
                'recordsTotal' => $records_total,
                'recordsFiltered' => $records_total,
                'data' => $data
            ];

            // Debug: Log the response for troubleshooting
            log_message('debug', 'Giblets fetch response: ' . json_encode($response));

            echo json_encode($response);

        } catch (Exception $e) {
            $response = [
                'draw' => intval($this->input->get_post('draw')),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => [],
                'error' => $e->getMessage()
            ];
            echo json_encode($response);
        }
    }

    /**
     * Generate actions for a giblets record
     */
    protected function _generate_actions($giblets_id)
    {
        $res = '<div class="dropdown text-right">';
        $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">';
        $res .= 'Actions <span class="caret"></span></button>';
        $res .= '<div class="dropdown-menu dropdown-menu-right">';

        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_SUPERVISOR])) {
            $res .= anchor('Giblets/edit/' . $giblets_id, 'Edit', 'title="Edit" class="dropdown-item text-left"');
            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $giblets_id . '">Delete</button>';
        }

        $res .= '</div></div>';
        return $res;
    }

    /**
     * Test method to verify controller is accessible
     */
    public function test()
    {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'success',
            'message' => 'Giblets fetch controller is working!',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Debug method with minimal data
     */
    public function debug()
    {
        header('Content-Type: application/json');

        $response = [
            'draw' => 1,
            'recordsTotal' => 2,
            'recordsFiltered' => 2,
            'data' => [
                [
                    'DT_RowId' => 'giblets_1',
                    'DT_RowClass' => 'giblets',
                    '0' => 'Test Customer',
                    '1' => '20250126001',
                    '2' => 'TEST-SKU',
                    '3' => 'Large',
                    '4' => 'A',
                    '5' => '1',
                    '6' => '10',
                    '7' => '2.50',
                    '8' => '<button class="btn btn-sm btn-danger">Delete</button>'
                ],
                [
                    'DT_RowId' => 'giblets_2',
                    'DT_RowClass' => 'giblets',
                    '0' => 'Another Customer',
                    '1' => '20250126002',
                    '2' => 'TEST-SKU2',
                    '3' => 'Medium',
                    '4' => 'B',
                    '5' => '2',
                    '6' => '20',
                    '7' => '5.00',
                    '8' => '<button class="btn btn-sm btn-danger">Delete</button>'
                ]
            ]
        ];

        echo json_encode($response);
    }
}
